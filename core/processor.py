from core.schema.chat_request import ChatRequest, Role
from util.common_util import get_ending_message, is_empty, get_cur_millis


def normalize_item_name(raw_name):
    if is_empty(raw_name):
        return raw_name

    raw_name = raw_name.strip()
    # 去掉字符串中的所有空格
    no_spaces = raw_name.replace(" ", "")
    # 将+换成 plus
    replace_plus = no_spaces.replace("+", "plus")
    # 将所有字符转换为小写
    lower_case = replace_plus.lower()
    return lower_case


def preprocess_request(chat_request: ChatRequest):
    if is_empty(chat_request.item_name):
        ending_selected_item = chat_request.chat_history[-1].messages[-1].selected_item
        if ending_selected_item is not None:
            chat_request.item_name = ending_selected_item.item_name
            chat_request.item_id = ending_selected_item.item_id
            chat_request.category_id = ending_selected_item.category_id
            chat_request.category_name = ending_selected_item.category_name
            chat_request.ending_selected_item = ending_selected_item
    # 当前没有用 id 而是用 normalized name 是因为 id 是后端自己随机定的，没有用在别处
    chat_request.item_name_normalized = normalize_item_name(chat_request.item_name)
    chat_request.ending_message = get_ending_message(chat_request)
    chat_request.str_message_list = get_str_message_list(chat_request)
    chat_request.request_receive_time = get_cur_millis()


def get_str_message_list(chat_request: ChatRequest):
    str_message_list = list()
    last_selected_item_name = None
    for chat_round in chat_request.chat_history:
        for message in chat_round.messages:
            # 如果用户切换了机型，插入一条「机型切换」的信息
            if should_insert_item_swap_message(chat_round.role, message, last_selected_item_name):
                # 接下来我想咨询一下 xxx
                # ToDo(hm): 这里也考虑多语言的影响
                str_message = f"{Role.USER.name}: Selanjutnya, saya ingin berkonsultasi dengan {last_selected_item_name}."
                str_message_list.append(str_message)
            str_message = f"{chat_round.role.name}: {message.to_prompt_str()}"
            str_message_list.append(str_message)
            if message.selected_item is not None:
                last_selected_item_name = message.selected_item.item_name
    return str_message_list

def should_insert_item_swap_message(role, message, last_selected_item_name):
    if role == Role.ASSISTANT:
        return False

    if message.selected_item is None:
        return False

    if is_empty(message.selected_item.item_name):
        return False

    if message.selected_item.item_name == last_selected_item_name:
        return False

    return True


if __name__ == '__main__':
    import json
    path = "/Users/<USER>/projects/inference/test/test_data/多轮对话/just4test.json"
    with open(path, "r") as f:
        data = json.load(f)
    chat_request_in = ChatRequest.from_dict(data)
    chat_request_in.str_message_list = get_str_message_list(chat_request_in)
    for str_msg in chat_request_in.str_message_list:
        print(str_msg)
