import getpass
import json
import socket
import uuid

import aiohttp
import streamlit as st

from config.run_config import RUN_CONFIG_DICT, DOMAIN, API_ACCESS_TOKEN
from core.schema.chat_base import MessageType
from core.schema.chat_request import ChatRequest, ChatRound, Message, Role, Area, Language, ResponseMode
from core.schema.translation_base import TranslationResponse, TranslationRequest
from core.schema.chat_response import EventType

from data_loader import load_item_name_list
from util.common_util import is_empty, decode_sse
from util.file_util import join_path
from util.llm_util import translate


def translation(project_dir):
    # 创建主要布局
    sidebar = st.sidebar
    header = st.container()
    chat_area = st.container()
    question_area = st.container()

    # 在侧边栏显示选择框
    with sidebar:
        path = join_path(project_dir, "config", "item_id_name.json")
        candidates, _ = load_item_name_list(path)
        candidates.append("UNK")

        ENV_NAME_DICT = {"本地": "local", "测试": "test", "预发": "preview", "生产": "prod"}
        language = st.selectbox("选择源语言", ["印尼语", "英语"], key='language')
        selected_item = st.selectbox("目标语言", ["中文"], key='product')
        env = st.selectbox("选择环境", ["本地", "预发", "测试", "生产"], key='env')
        env = ENV_NAME_DICT[env]
        debug_by_chinese = language == "中文"

        # 添加一个分隔线
        st.markdown("---")

        # 添加下载最近的chat_request按钮
        if "last_chat_request" in st.session_state and st.session_state.last_chat_request is not None:
            # 将chat_request转换为JSON字符串，并格式化以便于阅读
            chat_request_json = json.dumps(st.session_state.last_chat_request.to_dict(), indent=2, ensure_ascii=False)

            # 添加下载按钮
            st.download_button(
                label="下载最近的chat_request",
                data=chat_request_json,
                file_name="chat_request.json",
                mime="application/json"
            )

            # 添加复制按钮
            st.code(chat_request_json, language="json")

    # 在页面顶部显示标题
    with header:
        st.title("国际促销员 Copilot (翻译接口测试)")

    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # Initialize selected_question in session state if it doesn't exist
    if "selected_question" not in st.session_state:
        st.session_state.selected_question = None

    # Initialize last_chat_request in session state if it doesn't exist
    if "last_chat_request" not in st.session_state:
        st.session_state.last_chat_request = None

    # 在聊天区域显示聊天历史
    with chat_area:
        # Display chat messages from history on app rerun
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])

    # 在页面底部显示预设问题按钮和输入框
    # 注意：st.chat_input 必须在最外层调用，不能在 with 块中调用
    user_input = st.chat_input("在此输入待翻译文本", key='prompt')

    # 在输入框上方显示预设问题按钮
    with question_area:
        st.write("常见待翻译文本：")
        col1, col2 = st.columns(2)

        # Define the built-in questions
        question1 = "How big is the screen?"
        question2 = "Berapa berat ponsel itu?"

        # Create buttons for each question
        if col1.button(question1):
            st.session_state.selected_question = question1
            st.rerun()

        if col2.button(question2):
            st.session_state.selected_question = question2
            st.rerun()

    # Process input from buttons
    if st.session_state.selected_question is not None:
        button_prompt = st.session_state.selected_question
        # Reset selected_question to avoid processing it again
        st.session_state.selected_question = None

        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": button_prompt})
        translated_prompt = button_prompt
        if debug_by_chinese:
            translated_prompt = translate(button_prompt)
            st.session_state.messages.append({"role": "user", "content": translated_prompt})

        # Generate a request ID
        request_id = f"{getpass.getuser()}@{socket.gethostname()}-{uuid.uuid4()}"

        # Display user message in chat message container
        with chat_area:
            with st.chat_message("user"):
                st.markdown(f'{button_prompt}(chatRequestId: "{request_id}")')
                if debug_by_chinese:
                    st.markdown(f'{translated_prompt}(chatRequestId: "{request_id}")')

            # Display assistant response in chat message container
            with st.chat_message("assistant"):
                if debug_by_chinese:
                    response = st.write_stream(
                        response_generator(translated_prompt, selected_item, env, request_id)
                    )
                    st.session_state.messages.append({"role": "assistant", "content": response})
                    translated_response = translate(response, from_lang="印尼语", to_lang="中文")
                    st.session_state.messages.append(
                        {
                            "role": "assistant",
                            "content": translated_response,
                        }
                    )
                    st.markdown(translated_response)
                else:
                    response = st.write_stream(response_generator(button_prompt, selected_item, env, request_id))
                    st.session_state.messages.append({"role": "assistant", "content": response})

    # Process input from chat input
    if user_input:
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": user_input})
        translated_prompt = user_input
        if debug_by_chinese:
            translated_prompt = translate(user_input)
            st.session_state.messages.append({"role": "user", "content": translated_prompt})

        # Generate a request ID
        request_id = f"{getpass.getuser()}@{socket.gethostname()}-{uuid.uuid4()}"

        # Display user message in chat message container
        with chat_area:
            with st.chat_message("user"):
                st.markdown(f'{user_input}(chatRequestId: "{request_id}")')
                if debug_by_chinese:
                    st.markdown(f'{translated_prompt}(chatRequestId: "{request_id}")')

            # Display assistant response in chat message container
            with st.chat_message("assistant"):
                if debug_by_chinese:
                    response = st.write_stream(
                        response_generator(translated_prompt, selected_item, env, request_id)
                    )
                    st.session_state.messages.append({"role": "assistant", "content": response})
                    translated_response = translate(response, from_lang="印尼语", to_lang="中文")
                    st.session_state.messages.append(
                        {
                            "role": "assistant",
                            "content": translated_response,
                        }
                    )
                    st.markdown(translated_response)
                else:
                    response = st.write_stream(response_generator(user_input, selected_item, env, request_id))
                    st.session_state.messages.append({"role": "assistant", "content": response})


async def response_generator(query, product_id, env, request_id):
    url = f"http://{RUN_CONFIG_DICT[env][DOMAIN]}/api/v1/translate"
    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Authorization": RUN_CONFIG_DICT[env][API_ACCESS_TOKEN],
        "X-Request-ID": "streamlit",
    }

    # Create a ChatRequest object
    translate_request = TranslationRequest(
        request_id=request_id,
        content = query
    )

    # 保存chat_request到session state
    st.session_state.last_chat_request = translate_request

    # Convert the ChatRequest to a dictionary for the API request
    data = translate_request.to_dict()

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                yield f"请求失败，状态码: {response.status},响应内容:{await response.text()}"
                return

            async for line in response.content:
                # 去除多余的新行和空行
                chunk = line.decode("utf-8").strip()
                if is_empty(chunk):
                    continue
                
                yield json.loads(chunk)["content"]