import argparse
import streamlit as st

from frontend.bad_case_test import bad_case_test
from frontend.history_chat import history_chat
from frontend.label import label
from frontend.mock_chat import mock_chat
from frontend.ops import ops
from frontend.regression_test import regression_test
from frontend.translation import translation

OPS = "运维"
MOCK_CHAT = "单个请求"
HISTORY_CHAT = "历史对话"
REGRESSION_TEST = "批量处理"
BAD_CASE_TEST = "缺陷验证"
LABEL = "标注"
TRANSLATION = "翻译"


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--project_dir', type=str)
    args = parser.parse_args()
    project_dir = args.project_dir

    # Sidebar 选择模式
    mode = st.sidebar.selectbox("选择模式", [MOCK_CHAT, HISTORY_CHAT, REGRESSION_TEST, BAD_CASE_TEST, LABEL, OPS, TRANSLATION], index=0)

    if mode == OPS:
        ops(project_dir)
        return

    if mode == MOCK_CHAT:
        mock_chat(project_dir)
        return

    if mode == HISTORY_CHAT:
        history_chat()
        return

    if mode == REGRESSION_TEST:
        regression_test(project_dir)
        return

    if mode == BAD_CASE_TEST:
        bad_case_test(project_dir)
        return

    if mode == LABEL:
        label(project_dir)
        return
    
    if mode == TRANSLATION:
        translation(project_dir)
        return


if __name__ == '__main__':
    main()
